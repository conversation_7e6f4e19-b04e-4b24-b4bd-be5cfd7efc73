#!/bin/bash

# Supabase 模块化管理脚本
# 支持选择性启用/禁用 Supabase 模块
# 作者: Claude
# 版本: 1.0

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

# 配置文件路径
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CONFIG_FILE="$SCRIPT_DIR/supabase_modules.conf"
SUPABASE_DIR="$HOME/supabase/docker"
COMPOSE_FILE="$SUPABASE_DIR/docker-compose.yml"
CUSTOM_COMPOSE_DIR="$SCRIPT_DIR/compose_configs"

# 日志函数
log_info() { echo -e "${GREEN}[INFO]${NC} $1"; }
log_warn() { echo -e "${YELLOW}[WARN]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }
log_step() { echo -e "${BLUE}[STEP]${NC} $1"; }

# 检查依赖
check_dependencies() {
    if [[ ! -f "$CONFIG_FILE" ]]; then
        log_error "配置文件不存在: $CONFIG_FILE"
        exit 1
    fi
    
    if [[ ! -f "$COMPOSE_FILE" ]]; then
        log_error "Docker Compose文件不存在: $COMPOSE_FILE"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "docker-compose 命令未找到"
        exit 1
    fi
}

# 读取模块配置
read_module_config() {
    declare -gA MODULE_INFO
    declare -gA MODULE_DEPS
    declare -gA MODULE_RESOURCES
    declare -gA MODULE_ESSENTIAL
    declare -gA SCENARIOS
    
    while IFS= read -r line; do
        # 跳过注释和空行
        [[ "$line" =~ ^#.*$ ]] || [[ -z "$line" ]] && continue
        
        if [[ "$line" =~ ^SCENARIO_ ]]; then
            # 处理场景配置
            scenario_name=$(echo "$line" | cut -d':' -f1)
            scenario_desc=$(echo "$line" | cut -d':' -f2)
            scenario_modules=$(echo "$line" | cut -d':' -f3)
            SCENARIOS["$scenario_name"]="$scenario_desc:$scenario_modules"
        else
            # 处理模块配置
            IFS=':' read -r module desc deps resource essential <<< "$line"
            MODULE_INFO["$module"]="$desc"
            MODULE_DEPS["$module"]="$deps"
            MODULE_RESOURCES["$module"]="$resource"
            MODULE_ESSENTIAL["$module"]="$essential"
        fi
    done < "$CONFIG_FILE"
}

# 显示可用模块
show_modules() {
    echo "=========================================="
    echo "           可用的 Supabase 模块"
    echo "=========================================="
    
    echo -e "${CYAN}核心模块 (必需):${NC}"
    for module in "${!MODULE_INFO[@]}"; do
        if [[ "${MODULE_ESSENTIAL[$module]}" == "true" ]]; then
            echo "  ✓ $module - ${MODULE_INFO[$module]}"
        fi
    done
    
    echo ""
    echo -e "${CYAN}可选模块:${NC}"
    for module in "${!MODULE_INFO[@]}"; do
        if [[ "${MODULE_ESSENTIAL[$module]}" == "false" ]]; then
            resource="${MODULE_RESOURCES[$module]}"
            case "$resource" in
                "LOW") resource_icon="🟢" ;;
                "MEDIUM") resource_icon="🟡" ;;
                "HIGH") resource_icon="🔴" ;;
                *) resource_icon="⚪" ;;
            esac
            echo "  $resource_icon $module - ${MODULE_INFO[$module]} (资源: $resource)"
        fi
    done
    
    echo ""
    echo "资源使用说明:"
    echo "  🟢 LOW - 低资源消耗 (< 100MB RAM)"
    echo "  🟡 MEDIUM - 中等资源消耗 (100-500MB RAM)"
    echo "  🔴 HIGH - 高资源消耗 (> 500MB RAM)"
}

# 显示预定义场景
show_scenarios() {
    echo "=========================================="
    echo "           预定义使用场景"
    echo "=========================================="
    
    local counter=1
    for scenario in "${!SCENARIOS[@]}"; do
        IFS=':' read -r desc modules <<< "${SCENARIOS[$scenario]}"
        echo "$counter) ${scenario#SCENARIO_}: $desc"
        echo "   模块: $modules"
        echo ""
        ((counter++))
    done
}

# 解析依赖关系
resolve_dependencies() {
    local selected_modules="$1"
    local resolved_modules=""
    local to_process="$selected_modules"
    
    # 递归解析依赖
    while [[ -n "$to_process" ]]; do
        local current_module
        current_module=$(echo "$to_process" | cut -d',' -f1)
        to_process=$(echo "$to_process" | sed 's/^[^,]*,\?//')
        
        # 跳过已处理的模块
        if [[ "$resolved_modules" =~ $current_module ]]; then
            continue
        fi
        
        # 添加当前模块
        if [[ -n "$resolved_modules" ]]; then
            resolved_modules="$resolved_modules,$current_module"
        else
            resolved_modules="$current_module"
        fi
        
        # 添加依赖模块
        local deps="${MODULE_DEPS[$current_module]}"
        if [[ "$deps" != "none" && -n "$deps" ]]; then
            IFS=',' read -ra dep_array <<< "$deps"
            for dep in "${dep_array[@]}"; do
                if [[ ! "$resolved_modules" =~ $dep ]]; then
                    if [[ -n "$to_process" ]]; then
                        to_process="$to_process,$dep"
                    else
                        to_process="$dep"
                    fi
                fi
            done
        fi
    done
    
    echo "$resolved_modules"
}

# 验证模块存在性
validate_modules() {
    local modules="$1"
    local invalid_modules=""
    
    IFS=',' read -ra module_array <<< "$modules"
    for module in "${module_array[@]}"; do
        if [[ -z "${MODULE_INFO[$module]}" ]]; then
            if [[ -n "$invalid_modules" ]]; then
                invalid_modules="$invalid_modules,$module"
            else
                invalid_modules="$module"
            fi
        fi
    done
    
    if [[ -n "$invalid_modules" ]]; then
        log_error "无效的模块: $invalid_modules"
        return 1
    fi
    
    return 0
}

# 生成自定义 docker-compose 文件
generate_compose_file() {
    local modules="$1"
    local output_file="$2"
    
    log_step "生成自定义 docker-compose 配置..."
    
    # 创建输出目录
    mkdir -p "$(dirname "$output_file")"
    
    # 复制原始文件头部
    head -n 10 "$COMPOSE_FILE" > "$output_file"
    
    # 添加服务定义
    echo "" >> "$output_file"
    echo "services:" >> "$output_file"
    
    IFS=',' read -ra module_array <<< "$modules"
    for module in "${module_array[@]}"; do
        log_info "添加模块: $module (${MODULE_INFO[$module]})"
        
        # 从原始文件中提取服务定义
        extract_service_definition "$module" >> "$output_file"
    done
    
    # 添加网络和卷定义
    echo "" >> "$output_file"
    grep -A 1000 "^networks:" "$COMPOSE_FILE" >> "$output_file" 2>/dev/null || true
    
    log_info "自定义配置已生成: $output_file"
}

# 提取服务定义
extract_service_definition() {
    local service_name="$1"
    
    # 根据模块名映射到实际的服务名
    case "$service_name" in
        "db") service_name="db" ;;
        "auth") service_name="auth" ;;
        "rest") service_name="rest" ;;
        "realtime") service_name="realtime" ;;
        "storage") service_name="storage" ;;
        "imgproxy") service_name="imgproxy" ;;
        "meta") service_name="meta" ;;
        "functions") service_name="functions" ;;
        "analytics") service_name="analytics" ;;
        "studio") service_name="studio" ;;
        "kong") service_name="kong" ;;
        "vector") service_name="vector" ;;
        "supavisor") service_name="supavisor" ;;
    esac
    
    # 使用 awk 提取服务定义
    awk -v service="$service_name" '
    /^  [a-zA-Z]/ { 
        if (in_service) exit
        if ($1 == service":") {
            in_service = 1
            print
            next
        }
    }
    in_service && /^  [a-zA-Z]/ && $1 != service":" { exit }
    in_service { print }
    ' "$COMPOSE_FILE"
}

# 启动选定的模块
start_modules() {
    local modules="$1"
    local compose_file="$2"
    
    log_step "启动选定的模块..."
    
    cd "$SUPABASE_DIR"
    
    if [[ -n "$compose_file" ]]; then
        docker-compose -f "$compose_file" up -d
    else
        # 使用原始文件但只启动指定服务
        IFS=',' read -ra module_array <<< "$modules"
        docker-compose up -d "${module_array[@]}"
    fi
    
    log_info "模块启动完成"
}

# 停止所有服务
stop_all() {
    log_step "停止所有 Supabase 服务..."
    cd "$SUPABASE_DIR"
    docker-compose down
    log_info "所有服务已停止"
}

# 显示当前运行的模块
show_status() {
    log_step "当前运行的 Supabase 模块:"
    cd "$SUPABASE_DIR"
    docker-compose ps --format "table {{.Name}}\t{{.State}}\t{{.Ports}}"
}

# 交互式模块选择
interactive_module_selection() {
    echo "=========================================="
    echo "           交互式模块选择"
    echo "=========================================="

    echo "请选择要启用的模块 (用逗号分隔，例如: db,auth,rest):"
    echo ""

    show_modules
    echo ""

    read -p "输入模块名称: " selected_modules

    if [[ -z "$selected_modules" ]]; then
        log_error "未选择任何模块"
        return 1
    fi

    # 验证模块
    if ! validate_modules "$selected_modules"; then
        return 1
    fi

    # 解析依赖
    local resolved_modules
    resolved_modules=$(resolve_dependencies "$selected_modules")

    echo ""
    log_info "选定的模块: $selected_modules"
    log_info "包含依赖后: $resolved_modules"

    # 确认启动
    read -p "确认启动这些模块? (y/N): " confirm
    if [[ "$confirm" =~ ^[Yy]$ ]]; then
        local custom_compose="$CUSTOM_COMPOSE_DIR/custom-$(date +%Y%m%d-%H%M%S).yml"
        generate_compose_file "$resolved_modules" "$custom_compose"
        start_modules "$resolved_modules" "$custom_compose"
    else
        log_info "操作已取消"
    fi
}

# 场景式部署
deploy_scenario() {
    local scenario="$1"

    if [[ -z "${SCENARIOS[$scenario]}" ]]; then
        log_error "未知场景: $scenario"
        return 1
    fi

    IFS=':' read -r desc modules <<< "${SCENARIOS[$scenario]}"

    log_step "部署场景: ${scenario#SCENARIO_}"
    log_info "描述: $desc"
    log_info "包含模块: $modules"

    # 解析依赖
    local resolved_modules
    resolved_modules=$(resolve_dependencies "$modules")

    log_info "包含依赖后: $resolved_modules"

    # 生成配置并启动
    local custom_compose="$CUSTOM_COMPOSE_DIR/scenario-${scenario#SCENARIO_}-$(date +%Y%m%d-%H%M%S).yml"
    generate_compose_file "$resolved_modules" "$custom_compose"
    start_modules "$resolved_modules" "$custom_compose"
}

# 交互式场景选择
interactive_scenario_selection() {
    echo "=========================================="
    echo "           场景式部署"
    echo "=========================================="

    show_scenarios

    echo "请选择一个场景:"
    local scenarios=($(printf '%s\n' "${!SCENARIOS[@]}" | sort))
    local counter=1

    for scenario in "${scenarios[@]}"; do
        IFS=':' read -r desc modules <<< "${SCENARIOS[$scenario]}"
        echo "$counter) ${scenario#SCENARIO_}: $desc"
        ((counter++))
    done

    echo ""
    read -p "请选择场景编号 [1-${#scenarios[@]}]: " choice

    if [[ "$choice" =~ ^[0-9]+$ ]] && [[ "$choice" -ge 1 ]] && [[ "$choice" -le "${#scenarios[@]}" ]]; then
        local selected_scenario="${scenarios[$((choice-1))]}"
        deploy_scenario "$selected_scenario"
    else
        log_error "无效选择: $choice"
        return 1
    fi
}

# 显示帮助信息
show_help() {
    echo "Supabase 模块化管理工具"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  --modules, -m           显示可用模块"
    echo "  --scenarios, -s         显示预定义场景"
    echo "  --status                显示当前运行状态"
    echo "  --stop                  停止所有服务"
    echo "  --interactive, -i       交互式模块选择"
    echo "  --scenario <name>       部署指定场景"
    echo "  --deploy <modules>      部署指定模块 (逗号分隔)"
    echo "  --help, -h              显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 --scenario SCENARIO_AUTH_DB    # 部署数据库+认证场景"
    echo "  $0 --deploy db,auth,rest          # 部署指定模块"
    echo "  $0 --interactive                  # 交互式选择"
    echo ""
    echo "针对您的需求 (数据库+用户登录):"
    echo "  $0 --scenario SCENARIO_AUTH_DB    # 推荐使用此场景"
}

# 主函数
main() {
    log_info "Supabase 模块化管理工具启动..."

    # 检查依赖
    check_dependencies

    # 读取配置
    read_module_config

    # 创建自定义配置目录
    mkdir -p "$CUSTOM_COMPOSE_DIR"

    # 处理命令行参数
    case "${1:-}" in
        --modules|-m)
            show_modules
            ;;
        --scenarios|-s)
            show_scenarios
            ;;
        --status)
            show_status
            ;;
        --stop)
            stop_all
            ;;
        --interactive|-i)
            interactive_module_selection
            ;;
        --scenario)
            if [[ -n "$2" ]]; then
                deploy_scenario "$2"
            else
                log_error "请指定场景名称"
                show_scenarios
                exit 1
            fi
            ;;
        --deploy)
            if [[ -n "$2" ]]; then
                if validate_modules "$2"; then
                    local resolved_modules
                    resolved_modules=$(resolve_dependencies "$2")
                    log_info "部署模块: $2"
                    log_info "包含依赖后: $resolved_modules"

                    local custom_compose="$CUSTOM_COMPOSE_DIR/deploy-$(date +%Y%m%d-%H%M%S).yml"
                    generate_compose_file "$resolved_modules" "$custom_compose"
                    start_modules "$resolved_modules" "$custom_compose"
                fi
            else
                log_error "请指定要部署的模块"
                exit 1
            fi
            ;;
        --help|-h|"")
            show_help
            ;;
        *)
            log_error "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
