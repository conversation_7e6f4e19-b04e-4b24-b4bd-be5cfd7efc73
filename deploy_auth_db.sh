#!/bin/bash

# Supabase 数据库 + 认证 快速部署脚本
# 专为数据库和用户登录功能优化
# 作者: Claude

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
SUPABASE_DIR="$HOME/supabase/docker"
COMPOSE_FILE="$SCRIPT_DIR/docker-compose.auth-db.yml"
ENV_FILE="$SCRIPT_DIR/.env.auth-db"

# 日志函数
log_info() { echo -e "${GREEN}[INFO]${NC} $1"; }
log_warn() { echo -e "${YELLOW}[WARN]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }
log_step() { echo -e "${BLUE}[STEP]${NC} $1"; }

# 检查依赖
check_dependencies() {
    log_step "检查依赖..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose 未安装"
        exit 1
    fi
    
    if [[ ! -f "$COMPOSE_FILE" ]]; then
        log_error "Docker Compose 配置文件不存在: $COMPOSE_FILE"
        exit 1
    fi
    
    if [[ ! -f "$ENV_FILE" ]]; then
        log_error "环境配置文件不存在: $ENV_FILE"
        exit 1
    fi
    
    log_info "依赖检查通过"
}

# 生成安全密钥
generate_secrets() {
    log_step "生成安全密钥..."
    
    # 生成随机密码和密钥
    local jwt_secret=$(openssl rand -hex 32)
    local postgres_password=$(openssl rand -hex 16)
    local dashboard_password=$(openssl rand -hex 8)
    
    # 更新环境文件
    local temp_env="$ENV_FILE.tmp"
    
    while IFS= read -r line; do
        if [[ "$line" == *"your-super-secret-jwt-token-with-at-least-32-characters-long"* ]]; then
            echo "${line/your-super-secret-jwt-token-with-at-least-32-characters-long/$jwt_secret}"
        elif [[ "$line" == *"your-super-secret-and-long-postgres-password"* ]]; then
            echo "${line/your-super-secret-and-long-postgres-password/$postgres_password}"
        elif [[ "$line" == *"this_password_is_insecure_and_should_be_updated"* ]]; then
            echo "${line/this_password_is_insecure_and_should_be_updated/$dashboard_password}"
        else
            echo "$line"
        fi
    done < "$ENV_FILE" > "$temp_env" && mv "$temp_env" "$ENV_FILE"
    
    # 保存凭据
    cat > "$SCRIPT_DIR/auth_db_credentials.txt" << EOF
Supabase 数据库+认证 部署凭据
================================
PostgreSQL 密码: $postgres_password
Dashboard 密码: $dashboard_password
JWT 密钥: $jwt_secret

访问信息:
- PostgreSQL: localhost:5432
- REST API: http://localhost:3001
- Auth API: http://localhost:9999
- API Gateway: http://localhost:8000
- Meta API: http://localhost:8080

数据库连接:
- 主机: localhost
- 端口: 5432
- 数据库: postgres
- 用户: postgres
- 密码: $postgres_password

API 密钥:
- Anon Key: 在环境文件中查看
- Service Role Key: 在环境文件中查看

请妥善保存这些凭据！
EOF
    
    log_info "安全密钥已生成并保存到 auth_db_credentials.txt"
}

# 准备必要的目录和文件
prepare_environment() {
    log_step "准备环境..."
    
    # 创建必要的目录
    mkdir -p "$SCRIPT_DIR/volumes/db"
    mkdir -p "$SCRIPT_DIR/volumes/api"
    mkdir -p "$SCRIPT_DIR/volumes/logs"
    
    # 复制必要的配置文件 (如果原始 Supabase 目录存在)
    if [[ -d "$SUPABASE_DIR" ]]; then
        log_info "从原始 Supabase 配置复制必要文件..."
        
        # 复制数据库初始化脚本
        if [[ -d "$SUPABASE_DIR/volumes/db" ]]; then
            cp -r "$SUPABASE_DIR/volumes/db"/* "$SCRIPT_DIR/volumes/db/" 2>/dev/null || true
        fi
        
        # 复制 Kong 配置
        if [[ -f "$SUPABASE_DIR/volumes/api/kong.yml" ]]; then
            cp "$SUPABASE_DIR/volumes/api/kong.yml" "$SCRIPT_DIR/volumes/api/"
        fi
        
        # 复制 Vector 配置
        if [[ -f "$SUPABASE_DIR/volumes/logs/vector.yml" ]]; then
            cp "$SUPABASE_DIR/volumes/logs/vector.yml" "$SCRIPT_DIR/volumes/logs/"
        fi
    else
        log_warn "原始 Supabase 目录不存在，将使用默认配置"
        create_minimal_configs
    fi
    
    log_info "环境准备完成"
}

# 创建最小化配置文件
create_minimal_configs() {
    log_info "创建最小化配置文件..."
    
    # 创建简化的 Kong 配置
    cat > "$SCRIPT_DIR/volumes/api/kong.yml" << 'EOF'
_format_version: "1.1"

consumers:
  - username: anon
    keyauth_credentials:
      - key: ${SUPABASE_ANON_KEY}
  - username: service_role
    keyauth_credentials:
      - key: ${SUPABASE_SERVICE_KEY}

acls:
  - consumer: anon
    group: anon
  - consumer: service_role
    group: admin

services:
  - name: auth-v1-open
    url: http://auth:9999/verify
    routes:
      - name: auth-v1-open
        strip_path: true
        paths:
          - "/auth/v1/verify"
  
  - name: auth-v1-open-callback
    url: http://auth:9999/callback
    routes:
      - name: auth-v1-open-callback
        strip_path: true
        paths:
          - "/auth/v1/callback"

  - name: auth-v1
    _comment: "GoTrue: /auth/v1/* -> http://auth:9999/*"
    url: http://auth:9999/
    routes:
      - name: auth-v1-all
        strip_path: true
        paths:
          - "/auth/v1/"
    plugins:
      - name: cors

  - name: rest-v1
    _comment: "PostgREST: /rest/v1/* -> http://rest:3000/*"
    url: http://rest:3000/
    routes:
      - name: rest-v1-all
        strip_path: true
        paths:
          - "/rest/v1/"
    plugins:
      - name: cors
      - name: key-auth
        config:
          hide_credentials: false
      - name: acl
        config:
          hide_groups_header: true
          allow:
            - admin
            - anon

plugins:
  - name: cors
    config:
      origins:
        - "*"
      methods:
        - GET
        - POST
        - PUT
        - PATCH
        - DELETE
        - OPTIONS
      headers:
        - Accept
        - Accept-Version
        - Content-Length
        - Content-MD5
        - Content-Type
        - Date
        - X-Auth-Token
        - Authorization
        - X-Requested-With
        - Access-Control-Allow-Origin
        - apikey
        - range
      exposed_headers:
        - Content-Range
      credentials: true
      max_age: 3600
EOF

    # 创建简化的 Vector 配置
    cat > "$SCRIPT_DIR/volumes/logs/vector.yml" << 'EOF'
data_dir: /tmp/vector
sources:
  docker_logs:
    type: docker_logs
    include_containers:
      - supabase-auth
      - supabase-rest
      - supabase-db
      - supabase-kong

sinks:
  console:
    type: console
    inputs:
      - docker_logs
    encoding:
      codec: json
EOF
}

# 启动服务
start_services() {
    log_step "启动 Supabase 数据库+认证服务..."
    
    cd "$SCRIPT_DIR"
    
    # 使用自定义环境文件启动服务
    docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" up -d
    
    log_info "服务启动完成"
}

# 等待服务就绪
wait_for_services() {
    log_step "等待服务启动..."
    
    echo "等待数据库启动..."
    sleep 15
    
    echo "等待认证服务启动..."
    sleep 10
    
    # 检查服务状态
    cd "$SCRIPT_DIR"
    if docker-compose -f "$COMPOSE_FILE" ps | grep -q "Up"; then
        log_info "服务启动成功！"
        echo ""
        echo "运行中的服务:"
        docker-compose -f "$COMPOSE_FILE" ps --format "table {{.Name}}\t{{.State}}\t{{.Ports}}"
    else
        log_error "某些服务可能未正常启动"
        docker-compose -f "$COMPOSE_FILE" logs
    fi
}

# 显示部署信息
show_deployment_info() {
    echo ""
    echo "=========================================="
    echo "  Supabase 数据库+认证 部署成功！"
    echo "=========================================="
    echo ""
    echo "🎯 针对您的需求优化:"
    echo "✅ PostgreSQL 数据库"
    echo "✅ 用户认证和登录"
    echo "✅ REST API 访问"
    echo "✅ API 网关"
    echo ""
    echo "📡 访问端点:"
    echo "- PostgreSQL:     localhost:5432"
    echo "- REST API:       http://localhost:3001"
    echo "- Auth API:       http://localhost:9999"
    echo "- API Gateway:    http://localhost:8000"
    echo "- Meta API:       http://localhost:8080"
    echo ""
    echo "🔑 凭据信息:"
    echo "- 凭据文件: $SCRIPT_DIR/auth_db_credentials.txt"
    echo "- 环境配置: $SCRIPT_DIR/.env.auth-db"
    echo ""
    echo "🛠️ 管理命令:"
    echo "- 查看状态: cd $SCRIPT_DIR && docker-compose -f docker-compose.auth-db.yml ps"
    echo "- 查看日志: cd $SCRIPT_DIR && docker-compose -f docker-compose.auth-db.yml logs"
    echo "- 停止服务: cd $SCRIPT_DIR && docker-compose -f docker-compose.auth-db.yml down"
    echo "- 重启服务: cd $SCRIPT_DIR && docker-compose -f docker-compose.auth-db.yml restart"
    echo ""
    echo "💡 使用提示:"
    echo "1. 数据库连接信息请查看 auth_db_credentials.txt"
    echo "2. API 密钥在 .env.auth-db 文件中"
    echo "3. 认证端点: http://localhost:9999"
    echo "4. 数据访问端点: http://localhost:8000/rest/v1/"
    echo "=========================================="
}

# 主函数
main() {
    echo "=========================================="
    echo "  Supabase 数据库+认证 部署工具"
    echo "=========================================="
    echo ""
    echo "🎯 此配置专为以下需求优化:"
    echo "   ✓ PostgreSQL 数据库"
    echo "   ✓ 用户登录认证"
    echo "   ✓ REST API 访问"
    echo ""
    echo "💾 资源使用: 约 300-500MB RAM"
    echo "🚀 启动时间: 约 30-60 秒"
    echo ""
    
    read -p "确认开始部署? (y/N): " confirm
    if [[ ! "$confirm" =~ ^[Yy]$ ]]; then
        log_info "部署已取消"
        exit 0
    fi
    
    check_dependencies
    generate_secrets
    prepare_environment
    start_services
    wait_for_services
    show_deployment_info
    
    log_info "部署完成！"
}

# 执行主函数
main "$@"
