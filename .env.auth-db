# Supabase 数据库 + 认证 环境配置
# 专为数据库和用户登录功能优化的精简配置

############
# 数据库配置
############
POSTGRES_HOST=db
POSTGRES_PORT=5432
POSTGRES_DB=postgres
# 请修改为安全的密码
POSTGRES_PASSWORD=your-super-secret-and-long-postgres-password

############
# JWT 配置
############
# 请修改为安全的密钥 (至少32字符)
JWT_SECRET=your-super-secret-jwt-token-with-at-least-32-characters-long
JWT_EXPIRY=3600

############
# API 配置
############
API_EXTERNAL_URL=http://localhost:8000
SUPABASE_PUBLIC_URL=http://localhost:8000

# API 密钥 (基于 JWT_SECRET 生成)
ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0
SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImV4cCI6MTk4MzgxMjk5Nn0.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU

############
# 认证配置
############
SITE_URL=http://localhost:3000
ADDITIONAL_REDIRECT_URLS=
DISABLE_SIGNUP=false
ENABLE_EMAIL_SIGNUP=true
ENABLE_EMAIL_AUTOCONFIRM=true
ENABLE_ANONYMOUS_USERS=false

############
# 邮件配置 (可选)
############
SMTP_ADMIN_EMAIL=<EMAIL>
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-email-password
SMTP_SENDER_NAME=Supabase

# 邮件模板路径
MAILER_URLPATHS_INVITE=/auth/v1/verify
MAILER_URLPATHS_CONFIRMATION=/auth/v1/verify
MAILER_URLPATHS_RECOVERY=/auth/v1/verify
MAILER_URLPATHS_EMAIL_CHANGE=/auth/v1/verify

############
# Kong 网关配置
############
KONG_HTTP_PORT=8000
KONG_HTTPS_PORT=8443

############
# Dashboard 配置
############
DASHBOARD_USERNAME=supabase
DASHBOARD_PASSWORD=this_password_is_insecure_and_should_be_updated

############
# PostgREST 配置
############
PGRST_DB_SCHEMAS=public,storage,graphql_public

############
# Docker 配置
############
DOCKER_SOCKET_LOCATION=/var/run/docker.sock

############
# Studio 配置 (如果需要管理界面)
############
STUDIO_DEFAULT_ORGANIZATION=Default Organization
STUDIO_DEFAULT_PROJECT=Default Project

############
# 开发配置
############
# 设置为 true 启用详细日志
ENABLE_LOGS=false

# 注意事项:
# 1. 请务必修改 POSTGRES_PASSWORD 和 JWT_SECRET 为安全的值
# 2. 如果需要邮件功能，请配置 SMTP 相关参数
# 3. 生产环境请设置 DISABLE_SIGNUP=true 并配置适当的重定向URL
# 4. API_EXTERNAL_URL 应该设置为您的实际域名
